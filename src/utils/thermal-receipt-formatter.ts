import { Transaction } from '@/interfaces/transaction';
import { stringToLongDateFormatted } from './date-operations';
import { numberToCurrencyString } from './string-operations';

export interface ReceiptData {
  transaction: Transaction;
  businessName?: string;
  businessAddress?: string;
  businessPhone?: string;
}

export const formatThermalReceipt = (receiptData: ReceiptData): string => {
  const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

  const RECEIPT_WIDTH = 32;
  const SEPARATOR = '='.repeat(RECEIPT_WIDTH);
  const DASH_LINE = '-'.repeat(RECEIPT_WIDTH);

  const centerText = (text: string): string => {
    if (text.length >= RECEIPT_WIDTH) return text.substring(0, RECEIPT_WIDTH);
    const padding = Math.floor((RECEIPT_WIDTH - text.length) / 2);
    return ' '.repeat(padding) + text;
  };

  const formatTwoColumns = (left: string, right: string): string => {
    const maxLeftLength = RECEIPT_WIDTH - right.length;
    const leftTruncated =
      left.length > maxLeftLength ? left.substring(0, maxLeftLength - 3) + '...' : left;
    const padding = RECEIPT_WIDTH - leftTruncated.length - right.length;
    return leftTruncated + ' '.repeat(Math.max(0, padding)) + right;
  };

  let receipt = '';

  receipt += '\n';
  receipt += centerText(businessName) + '\n';
  if (businessAddress) {
    receipt += centerText(businessAddress) + '\n';
  }
  if (businessPhone) {
    receipt += centerText(businessPhone) + '\n';
  }
  receipt += '\n';
  receipt += centerText('RECIBO DE TRANSACCION 333') + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId) + '\n';
  receipt += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate)) + '\n';
  receipt += '\n';
  receipt += DASH_LINE + '\n';
  receipt += centerText('DETALLE DE COMPRA') + '\n';
  receipt += DASH_LINE + '\n';
  receipt;

  receipt +=
    formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount)) + '\n';

  if (transaction.interests > 0) {
    receipt += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests)) + '\n';
  }

  if (transaction.IVAAmount > 0) {
    receipt += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount)) + '\n';
  }

  receipt += DASH_LINE + '\n';
  receipt +=
    formatTwoColumns(
      'TOTAL:',
      numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount),
    ) + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += centerText('INFORMACION DE PAGO') + '\n';
  receipt += DASH_LINE + '\n';
  receipt += formatTwoColumns('Plazo:', '15 dias') + '\n';
  receipt +=
    formatTwoColumns('Fecha limite:', stringToLongDateFormatted(transaction.paymentDate)) + '\n';
  receipt += '\n';
  receipt += centerText('El cliente pagará después') + '\n';
  receipt += centerText('en el portal de Pagos de Propaga') + '\n';
  receipt += '\n';

  receipt += SEPARATOR + '\n';
  receipt += centerText('Gracias por su compra') + '\n';
  receipt += centerText('Conserve este recibo') + '\n';
  receipt += '\n';
  receipt += '\n';
  receipt += '\n';

  return receipt;
};

export const formatHtmlReceipt = (receiptData: ReceiptData): string => {
  const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

  const centerText = (text: string): string => {
    return `<div class="center-text">${text}</div>`;
  };

  const formatTwoColumns = (left: string, right: string): string => {
    return `<div class="two-columns"><span class="left-column">${left}</span><span class="right-column">${right}</span></div>`;
  };

  const separator = '<div class="separator">================================</div>';
  const dashLine = '<div class="dash-line">--------------------------------</div>';

  let html = `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recibo de Transacción</title>
    <style>
        @media print {
            body {
                margin: 0;
                background: white !important;
            }
            .receipt-container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 5mm !important;
                width: 58mm !important;
                background: white !important;
                border: none !important;
                border-radius: 0 !important;
            }
            .no-print { display: none !important; }
            .perforated-top, .perforated-bottom {
                border: none !important;
            }
        }

        body {
            font-family: 'Consolas', 'Monaco', 'Lucida Console', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
            color: #1a1a1a;
        }

        .receipt-container {
            background: linear-gradient(180deg, #fefefe 0%, #f9f9f9 100%);
            width: 340px;
            margin: 0 auto;
            padding: 18px 16px;
            box-shadow:
                0 4px 8px rgba(0,0,0,0.12),
                0 2px 4px rgba(0,0,0,0.08),
                inset 0 1px 0 rgba(255,255,255,0.5);
            border-radius: 0 0 6px 6px;
            font-size: 10px;
            line-height: 1.1;
            position: relative;
            border-left: 1px solid #e0e0e0;
            border-right: 1px solid #e0e0e0;
            border-bottom: 1px solid #d0d0d0;
            background-image:
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 11px,
                    rgba(0,0,0,0.02) 11px,
                    rgba(0,0,0,0.02) 12px
                );
        }

        .perforated-top {
            position: absolute;
            top: -1px;
            left: 0;
            right: 0;
            height: 8px;
            background: repeating-linear-gradient(
                90deg,
                #d0d0d0 0px,
                #d0d0d0 3px,
                transparent 3px,
                transparent 6px
            );
            border-radius: 0;
        }

        .perforated-bottom {
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 8px;
            background: repeating-linear-gradient(
                90deg,
                #d0d0d0 0px,
                #d0d0d0 3px,
                transparent 3px,
                transparent 6px
            );
            border-radius: 0 0 6px 6px;
        }

        .center-text {
            text-align: center;
            margin: 1px 0;
            letter-spacing: 0.5px;
            padding: 0 2px;
        }

        .two-columns {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin: 1px 0;
            width: 100%;
            letter-spacing: 0.3px;
            padding: 0 2px;
            gap: 8px;
        }

        .left-column {
            flex: 1 1 auto;
            text-align: left;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .right-column {
            flex: 0 0 auto;
            text-align: right;
            white-space: nowrap;
            font-weight: 500;
            margin-left: auto;
        }

        .separator, .dash-line {
            text-align: center;
            margin: 2px 0;
            font-weight: bold;
            letter-spacing: -0.5px;
            color: #2a2a2a;
        }

        .business-header {
            font-weight: bold;
            font-size: 12px;
            letter-spacing: 1px;
            color: #1a1a1a;
        }

        .receipt-title {
            font-weight: bold;
            font-size: 11px;
            letter-spacing: 0.8px;
            color: #1a1a1a;
        }

        .section-title {
            font-weight: bold;
            font-size: 10px;
            letter-spacing: 0.5px;
            color: #2a2a2a;
        }

        .total-amount {
            font-weight: bold;
            font-size: 11px;
            color: #1a1a1a;
            letter-spacing: 0.3px;
        }

        .spacing {
            height: 6px;
        }

        .print-button {
            margin: 20px auto;
            display: block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .print-button:hover {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.25);
        }

        .print-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">Imprimir Recibo</button>
    <div class="receipt-container">
        <div class="perforated-top"></div>`;

  // Business header
  html += '<div class="spacing"></div>';
  html += centerText(`<span class="business-header">${businessName}</span>`);

  if (businessAddress) {
    html += centerText(businessAddress);
  }

  if (businessPhone) {
    html += centerText(businessPhone);
  }

  html += '<div class="spacing"></div>';
  html += centerText('<span class="receipt-title">RECIBO DE TRANSACCION</span>');
  html += separator;
  html += '<div class="spacing"></div>';

  // Transaction details
  html += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId);
  html += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate));
  html += '<div class="spacing"></div>';

  // Purchase details section
  html += dashLine;
  html += centerText('<span class="section-title">DETALLE DE COMPRA</span>');
  html += dashLine;

  html += formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount));

  if (transaction.interests > 0) {
    html += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests));
  }

  if (transaction.IVAAmount > 0) {
    html += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount));
  }

  html += dashLine;
  html += formatTwoColumns(
    '<span class="total-amount">TOTAL:</span>',
    `<span class="total-amount">${numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount)}</span>`,
  );
  html += separator;
  html += '<div class="spacing"></div>';

  // Payment information
  html += centerText('<span class="section-title">INFORMACION DE PAGO</span>');
  html += dashLine;
  html += formatTwoColumns('Plazo:', '15 dias');
  html += formatTwoColumns('Fecha limite:', stringToLongDateFormatted(transaction.paymentDate));
  html += '<div class="spacing"></div>';
  html += centerText('El cliente pagará después');
  html += centerText('en el portal de Pagos de Propaga');
  html += '<div class="spacing"></div>';

  // Footer
  html += separator;
  html += centerText('Gracias por su compra');
  html += centerText('Conserve este recibo');
  html += '<div class="spacing"></div>';
  html += '<div class="spacing"></div>';
  html += '<div class="spacing"></div>';

  html += `
        <div class="perforated-bottom"></div>
    </div>
</body>
</html>`;

  return html;
};

export const formatReceiptPreview = (receiptData: ReceiptData): string => {
  const thermalReceipt = formatThermalReceipt(receiptData);
  return thermalReceipt;
};
